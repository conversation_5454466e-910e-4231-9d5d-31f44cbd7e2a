#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web界面修复
"""

import requests
import json

def test_dashboard_receive_email():
    """测试主页面接收邮件按钮"""
    print("🧪 测试主页面接收邮件功能...")
    
    # 首先需要登录
    session = requests.Session()
    
    # 获取登录页面
    login_url = "http://localhost:5000/auth/login"
    response = session.get(login_url)
    print(f"登录页面状态: {response.status_code}")
    
    # 尝试登录（使用测试用户）
    login_data = {
        "username": "testuser",
        "password": "testpass",
        "csrf_token": ""  # 在实际应用中需要从页面提取CSRF token
    }
    
    # 由于CSRF保护，我们直接测试API
    api_url = "http://localhost:5000/api/cli/email/receive"
    
    try:
        response = session.post(api_url, json={"type": "latest"})
        print(f"API响应状态: {response.status_code}")
        
        if response.status_code == 401:
            print("❌ 需要登录才能访问API")
            return False
        
        data = response.json()
        print(f"API响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        if not data.get("success"):
            error_msg = data.get("error", "")
            if "配置邮箱" in error_msg:
                print("✅ 正确检测到需要配置邮箱")
                return True
            else:
                print(f"❌ 意外的错误信息: {error_msg}")
                return False
        else:
            print("✅ API调用成功")
            return True
            
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        return False

def test_inbox_page():
    """测试收件箱页面"""
    print("\n🧪 测试收件箱页面...")
    
    try:
        response = requests.get("http://localhost:5000/email/inbox")
        print(f"收件箱页面状态: {response.status_code}")
        
        if response.status_code == 302:
            print("✅ 正确重定向到登录页面")
            return True
        elif response.status_code == 200:
            print("✅ 页面加载成功")
            return True
        else:
            print(f"❌ 意外的状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 页面访问失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Web界面修复...")
    
    results = []
    
    # 测试主页面接收邮件功能
    results.append(test_dashboard_receive_email())
    
    # 测试收件箱页面
    results.append(test_inbox_page())
    
    # 总结结果
    print(f"\n📊 测试结果:")
    print(f"✅ 成功: {sum(results)}")
    print(f"❌ 失败: {len(results) - sum(results)}")
    print(f"📈 成功率: {sum(results)/len(results)*100:.1f}%")
    
    if all(results):
        print("\n🎉 所有测试通过！Web界面修复成功。")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
