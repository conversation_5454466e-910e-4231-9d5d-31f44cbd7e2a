{% extends "base.html" %} {% block title %}邮箱仪表板 - {{ app_name }}{%
endblock %} {% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-tachometer-alt me-2"></i>邮箱仪表板</h2>
        <div class="d-flex gap-2">
          <a href="{{ url_for('email.compose') }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>写邮件
          </a>
          <a
            href="{{ url_for('email_auth.logout') }}"
            class="btn btn-outline-secondary"
          >
            <i class="fas fa-sign-out-alt me-1"></i>退出
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- 用户信息 -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title"><i class="fas fa-user me-2"></i>账户信息</h5>
          <div class="row">
            <div class="col-md-4">
              <strong>邮箱地址：</strong><br />
              <span class="text-primary">{{ user_info.email }}</span>
            </div>
            <div class="col-md-4">
              <strong>邮箱服务商：</strong><br />
              <span class="badge bg-info">{{ user_info.provider }}</span>
            </div>
            <div class="col-md-4">
              <strong>登录时间：</strong><br />
              <span class="text-muted">{{ user_info.login_time }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 连接状态 -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card">
        <div class="card-body text-center">
          <h5 class="card-title">
            <i class="fas fa-paper-plane me-2"></i>发送邮件
          </h5>
          {% if smtp_ok %}
          <div class="text-success">
            <i class="fas fa-check-circle fa-2x"></i>
            <p class="mt-2">SMTP 连接正常</p>
          </div>
          {% else %}
          <div class="text-warning">
            <i class="fas fa-exclamation-triangle fa-2x"></i>
            <p class="mt-2">SMTP 连接异常</p>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card">
        <div class="card-body text-center">
          <h5 class="card-title"><i class="fas fa-inbox me-2"></i>接收邮件</h5>
          {% if pop3_ok %}
          <div class="text-success">
            <i class="fas fa-check-circle fa-2x"></i>
            <p class="mt-2">POP3 连接正常</p>
          </div>
          {% else %}
          <div class="text-warning">
            <i class="fas fa-exclamation-triangle fa-2x"></i>
            <p class="mt-2">POP3 连接异常</p>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- 邮件统计 -->
  <div class="row mb-4">
    <div class="col-6 col-md-3 mb-3">
      <div class="card bg-primary text-white h-100">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">收到邮件</h6>
              <h3 id="total-received">{{ email_stats.total }}</h3>
            </div>
            <div class="align-self-center">
              <i class="fas fa-inbox fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-6 col-md-3 mb-3">
      <div class="card bg-warning text-white h-100">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">未读邮件</h6>
              <h3 id="unread-count">{{ email_stats.unread }}</h3>
            </div>
            <div class="align-self-center">
              <i class="fas fa-envelope-open fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-6 col-md-3 mb-3">
      <div class="card bg-success text-white h-100">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">已发送</h6>
              <h3 id="total-sent">0</h3>
            </div>
            <div class="align-self-center">
              <i class="fas fa-paper-plane fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-6 col-md-3 mb-3">
      <div class="card bg-danger text-white h-100">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">垃圾邮件</h6>
              <h3 id="spam-count">0</h3>
            </div>
            <div class="align-self-center">
              <i class="fas fa-shield-alt fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 快速操作 -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>快速操作</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-6 col-md-3 mb-3">
              <a
                href="{{ url_for('email.inbox') }}"
                class="btn btn-outline-primary btn-lg w-100 h-100 d-flex flex-column justify-content-center"
              >
                <i class="fas fa-inbox fa-2x mb-2"></i>
                <span>收件箱</span>
              </a>
            </div>
            <div class="col-6 col-md-3 mb-3">
              <a
                href="{{ url_for('email.compose') }}"
                class="btn btn-outline-success btn-lg w-100 h-100 d-flex flex-column justify-content-center"
              >
                <i class="fas fa-edit fa-2x mb-2"></i>
                <span>写邮件</span>
              </a>
            </div>
            <div class="col-6 col-md-3 mb-3">
              <a
                href="{{ url_for('email.sent') }}"
                class="btn btn-outline-info btn-lg w-100 h-100 d-flex flex-column justify-content-center"
              >
                <i class="fas fa-paper-plane fa-2x mb-2"></i>
                <span>已发送</span>
              </a>
            </div>
            <div class="col-6 col-md-3 mb-3">
              <button
                onclick="receiveEmails()"
                class="btn btn-outline-warning btn-lg w-100 h-100 d-flex flex-column justify-content-center"
              >
                <i class="fas fa-download fa-2x mb-2"></i>
                <span>接收邮件</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- CLI功能集成区域 -->
  <div class="row mt-4">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fas fa-search me-2"></i>快速搜索
          </h5>
        </div>
        <div class="card-body">
          <form onsubmit="quickSearch(event)">
            <div class="input-group">
              <select
                class="form-select"
                id="search-type"
                style="max-width: 120px"
              >
                <option value="sender">发件人</option>
                <option value="subject">主题</option>
                <option value="content">内容</option>
              </select>
              <input
                type="text"
                class="form-control"
                id="search-keyword"
                placeholder="输入搜索关键词..."
              />
              <button class="btn btn-outline-secondary" type="submit">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </form>
          <div id="search-results" class="mt-3" style="display: none">
            <!-- 搜索结果将显示在这里 -->
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fas fa-shield-alt me-2"></i>垃圾邮件管理
          </h5>
        </div>
        <div class="card-body">
          <p class="text-muted">
            当前垃圾邮件过滤阈值: <span id="spam-threshold">3.0</span>
          </p>
          <div class="btn-group w-100" role="group">
            <button
              class="btn btn-outline-info btn-sm"
              onclick="viewSpamKeywords()"
            >
              查看关键词
            </button>
            <button
              class="btn btn-outline-warning btn-sm"
              onclick="addSpamKeyword()"
            >
              添加关键词
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    console.log("邮箱仪表板已加载");
    // 加载邮件统计
    loadEmailStats();
    // 加载垃圾邮件设置
    loadSpamSettings();
  });

  // 加载邮件统计
  function loadEmailStats() {
    fetch("/api/cli/email/stats")
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          const stats = data.data;
          document.getElementById("total-received").textContent =
            stats.total_received || 0;
          document.getElementById("unread-count").textContent =
            stats.unread_received || 0;
          document.getElementById("total-sent").textContent =
            stats.total_sent || 0;
          document.getElementById("spam-count").textContent =
            stats.spam_count || 0;
        }
      })
      .catch((error) => {
        console.error("加载邮件统计失败:", error);
      });
  }

  // 接收邮件
  function receiveEmails() {
    const button = event.target.closest("button");
    const originalText = button.innerHTML;

    // 显示加载状态
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 接收中...';
    button.disabled = true;

    fetch("/api/cli/email/receive", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "latest",
        count: 10,
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          alert("邮件接收请求已提交！");
          // 刷新邮件统计
          loadEmailStats();
        } else {
          const errorMsg = data.error;
          if (errorMsg.includes("配置邮箱")) {
            if (confirm("需要先配置邮箱才能接收邮件。是否前往邮箱登录页面？")) {
              window.location.href = "/auth/email_login";
            }
          } else {
            alert("接收邮件失败: " + errorMsg);
          }
        }
      })
      .catch((error) => {
        console.error("接收邮件失败:", error);
        alert("接收邮件时出错");
      })
      .finally(() => {
        // 恢复按钮状态
        button.innerHTML = originalText;
        button.disabled = false;
      });
  }

  // 快速搜索
  function quickSearch(event) {
    event.preventDefault();

    const searchType = document.getElementById("search-type").value;
    const keyword = document.getElementById("search-keyword").value.trim();

    if (!keyword) {
      alert("请输入搜索关键词");
      return;
    }

    const resultsDiv = document.getElementById("search-results");
    resultsDiv.innerHTML =
      '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 搜索中...</div>';
    resultsDiv.style.display = "block";

    fetch("/api/cli/email/search", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: searchType,
        keyword: keyword,
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          displaySearchResults(data.data);
        } else {
          resultsDiv.innerHTML =
            '<div class="alert alert-danger">搜索失败: ' +
            data.error +
            "</div>";
        }
      })
      .catch((error) => {
        console.error("搜索失败:", error);
        resultsDiv.innerHTML =
          '<div class="alert alert-danger">搜索时出错</div>';
      });
  }

  // 显示搜索结果
  function displaySearchResults(data) {
    const resultsDiv = document.getElementById("search-results");

    if (data.results.length === 0) {
      resultsDiv.innerHTML =
        '<div class="alert alert-info">未找到匹配的邮件</div>';
      return;
    }

    let html = `<div class="alert alert-success">找到 ${data.count} 封邮件</div>`;
    html += '<div class="list-group">';

    data.results.slice(0, 5).forEach((email) => {
      const subject = email.subject || "(无主题)";
      const sender = email.from_addr || "未知发件人";
      const date = email.date || "未知日期";

      html += `
        <div class="list-group-item">
          <div class="d-flex w-100 justify-content-between">
            <h6 class="mb-1">${subject}</h6>
            <small>${date}</small>
          </div>
          <p class="mb-1">发件人: ${sender}</p>
        </div>
      `;
    });

    html += "</div>";

    if (data.results.length > 5) {
      html += `<div class="text-center mt-2"><small class="text-muted">还有 ${
        data.results.length - 5
      } 封邮件...</small></div>`;
    }

    resultsDiv.innerHTML = html;
  }

  // 加载垃圾邮件设置
  function loadSpamSettings() {
    fetch("/api/cli/spam/keywords")
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          document.getElementById("spam-threshold").textContent =
            data.data.threshold;
        }
      })
      .catch((error) => {
        console.error("加载垃圾邮件设置失败:", error);
      });
  }

  // 查看垃圾邮件关键词
  function viewSpamKeywords() {
    fetch("/api/cli/spam/keywords")
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          const keywords = data.data.keywords;
          let message = "垃圾邮件关键词:\n\n";

          if (keywords.subject && keywords.subject.length > 0) {
            message += "主题关键词: " + keywords.subject.join(", ") + "\n\n";
          }
          if (keywords.body && keywords.body.length > 0) {
            message += "正文关键词: " + keywords.body.join(", ") + "\n\n";
          }
          if (keywords.sender && keywords.sender.length > 0) {
            message += "发件人关键词: " + keywords.sender.join(", ") + "\n\n";
          }

          if (message === "垃圾邮件关键词:\n\n") {
            message += "暂无关键词";
          }

          alert(message);
        } else {
          alert("获取关键词失败: " + data.error);
        }
      })
      .catch((error) => {
        console.error("获取关键词失败:", error);
        alert("获取关键词时出错");
      });
  }

  // 添加垃圾邮件关键词
  function addSpamKeyword() {
    const category = prompt(
      "请选择关键词类别:\n1. subject (主题)\n2. body (正文)\n3. sender (发件人)\n\n请输入类别名称:"
    );

    if (!category || !["subject", "body", "sender"].includes(category)) {
      alert("无效的类别");
      return;
    }

    const keyword = prompt("请输入要添加的关键词:");

    if (!keyword || !keyword.trim()) {
      alert("关键词不能为空");
      return;
    }

    fetch("/api/cli/spam/keywords", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        category: category,
        keyword: keyword.trim(),
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          alert(data.message);
        } else {
          alert("添加关键词失败: " + data.error);
        }
      })
      .catch((error) => {
        console.error("添加关键词失败:", error);
        alert("添加关键词时出错");
      });
  }
</script>
{% endblock %}
