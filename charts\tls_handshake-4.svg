<svg aria-roledescription="gantt" role="graphics-document document" style="max-width: 784px; background-color: white;" viewBox="0 0 784 364" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .mermaid-main-font{font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .exclude-range{fill:#eeeeee;}#my-svg .section{stroke:none;opacity:0.2;}#my-svg .section0{fill:rgba(102, 102, 255, 0.49);}#my-svg .section2{fill:#fff400;}#my-svg .section1,#my-svg .section3{fill:white;opacity:0.2;}#my-svg .sectionTitle0{fill:#333;}#my-svg .sectionTitle1{fill:#333;}#my-svg .sectionTitle2{fill:#333;}#my-svg .sectionTitle3{fill:#333;}#my-svg .sectionTitle{text-anchor:start;font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .grid .tick{stroke:lightgrey;opacity:0.8;shape-rendering:crispEdges;}#my-svg .grid .tick text{font-family:"trebuchet ms",verdana,arial,sans-serif;fill:#333;}#my-svg .grid path{stroke-width:0;}#my-svg .today{fill:none;stroke:red;stroke-width:2px;}#my-svg .task{stroke-width:2;}#my-svg .taskText{text-anchor:middle;font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .taskTextOutsideRight{fill:black;text-anchor:start;font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .taskTextOutsideLeft{fill:black;text-anchor:end;}#my-svg .task.clickable{cursor:pointer;}#my-svg .taskText.clickable{cursor:pointer;fill:#003163!important;font-weight:bold;}#my-svg .taskTextOutsideLeft.clickable{cursor:pointer;fill:#003163!important;font-weight:bold;}#my-svg .taskTextOutsideRight.clickable{cursor:pointer;fill:#003163!important;font-weight:bold;}#my-svg .taskText0,#my-svg .taskText1,#my-svg .taskText2,#my-svg .taskText3{fill:white;}#my-svg .task0,#my-svg .task1,#my-svg .task2,#my-svg .task3{fill:#8a90dd;stroke:#534fbc;}#my-svg .taskTextOutside0,#my-svg .taskTextOutside2{fill:black;}#my-svg .taskTextOutside1,#my-svg .taskTextOutside3{fill:black;}#my-svg .active0,#my-svg .active1,#my-svg .active2,#my-svg .active3{fill:#bfc7ff;stroke:#534fbc;}#my-svg .activeText0,#my-svg .activeText1,#my-svg .activeText2,#my-svg .activeText3{fill:black!important;}#my-svg .done0,#my-svg .done1,#my-svg .done2,#my-svg .done3{stroke:grey;fill:lightgrey;stroke-width:2;}#my-svg .doneText0,#my-svg .doneText1,#my-svg .doneText2,#my-svg .doneText3{fill:black!important;}#my-svg .crit0,#my-svg .crit1,#my-svg .crit2,#my-svg .crit3{stroke:#ff8888;fill:red;stroke-width:2;}#my-svg .activeCrit0,#my-svg .activeCrit1,#my-svg .activeCrit2,#my-svg .activeCrit3{stroke:#ff8888;fill:#bfc7ff;stroke-width:2;}#my-svg .doneCrit0,#my-svg .doneCrit1,#my-svg .doneCrit2,#my-svg .doneCrit3{stroke:#ff8888;fill:lightgrey;stroke-width:2;cursor:pointer;shape-rendering:crispEdges;}#my-svg .milestone{transform:rotate(45deg) scale(0.8,0.8);}#my-svg .milestoneText{font-style:italic;}#my-svg .doneCritText0,#my-svg .doneCritText1,#my-svg .doneCritText2,#my-svg .doneCritText3{fill:black!important;}#my-svg .activeCritText0,#my-svg .activeCritText1,#my-svg .activeCritText2,#my-svg .activeCritText3{fill:black!important;}#my-svg .titleText{text-anchor:middle;font-size:18px;fill:#333;font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g/><g text-anchor="middle" font-family="sans-serif" font-size="10" fill="none" transform="translate(75, 314)" class="grid"><path d="M0.5,-279V0.5H634.5V-279" stroke="currentColor" class="domain"/><g transform="translate(0.5,0)" opacity="1" class="tick"><line y2="-279" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">0</text></g><g transform="translate(63.5,0)" opacity="1" class="tick"><line y2="-279" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">15</text></g><g transform="translate(127.5,0)" opacity="1" class="tick"><line y2="-279" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">30</text></g><g transform="translate(190.5,0)" opacity="1" class="tick"><line y2="-279" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">45</text></g><g transform="translate(254.5,0)" opacity="1" class="tick"><line y2="-279" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">60</text></g><g transform="translate(317.5,0)" opacity="1" class="tick"><line y2="-279" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">75</text></g><g transform="translate(380.5,0)" opacity="1" class="tick"><line y2="-279" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">90</text></g><g transform="translate(444.5,0)" opacity="1" class="tick"><line y2="-279" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">105</text></g><g transform="translate(507.5,0)" opacity="1" class="tick"><line y2="-279" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">120</text></g><g transform="translate(571.5,0)" opacity="1" class="tick"><line y2="-279" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">135</text></g><g transform="translate(634.5,0)" opacity="1" class="tick"><line y2="-279" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">150</text></g></g><g><rect class="section section0" height="24" width="746.5" y="48" x="0"/><rect class="section section1" height="24" width="746.5" y="168" x="0"/><rect class="section section2" height="24" width="746.5" y="240" x="0"/><rect class="section section2" height="24" width="746.5" y="288" x="0"/><rect class="section section2" height="24" width="746.5" y="264" x="0"/><rect class="section section0" height="24" width="746.5" y="72" x="0"/><rect class="section section1" height="24" width="746.5" y="192" x="0"/><rect class="section section1" height="24" width="746.5" y="216" x="0"/><rect class="section section0" height="24" width="746.5" y="96" x="0"/><rect class="section section0" height="24" width="746.5" y="120" x="0"/><rect class="section section0" height="24" width="746.5" y="144" x="0"/></g><g><rect class="task milestone  task0" transform-origin="75px 60px" height="20" width="20" y="50" x="65" ry="3" rx="3" id="ch12"/><rect class="task milestone  task1" transform-origin="75px 180px" height="20" width="20" y="170" x="65" ry="3" rx="3" id="ch13"/><rect class="task milestone  task2" transform-origin="75px 252px" height="20" width="20" y="242" x="65" ry="3" rx="3" id="early"/><rect class="task milestone  task2" transform-origin="75px 300px" height="20" width="20" y="290" x="65" ry="3" rx="3" id="app0"/><rect class="task milestone  task2" transform-origin="181px 276px" height="20" width="20" y="266" x="171" ry="3" rx="3" id="resp"/><rect class="task milestone  task0" transform-origin="286px 84px" height="20" width="20" y="74" x="276" ry="3" rx="3" id="sh12"/><rect class="task milestone  task1" transform-origin="286px 204px" height="20" width="20" y="194" x="276" ry="3" rx="3" id="fin13"/><rect class="task milestone  task1" transform-origin="286px 228px" height="20" width="20" y="218" x="276" ry="3" rx="3" id="app13"/><rect class="task milestone  task0" transform-origin="498px 108px" height="20" width="20" y="98" x="488" ry="3" rx="3" id="cke12"/><rect class="task milestone  task0" transform-origin="709px 132px" height="20" width="20" y="122" x="699" ry="3" rx="3" id="fin12"/><rect class="task milestone  task0" transform-origin="709px 156px" height="20" width="20" y="146" x="699" ry="3" rx="3" id="app12"/><text class="taskTextOutsideRight taskTextOutside0  milestoneText width-55.0166015625" y="63.5" x="90" font-size="11" id="ch12-text">ClientHello           </text><text class="taskTextOutsideRight taskTextOutside1  milestoneText width-112.09375" y="183.5" x="90" font-size="11" id="ch13-text">ClientHello + KeyShare </text><text class="taskTextOutsideRight taskTextOutside2  milestoneText width-112.31591796875" y="255.5" x="90" font-size="11" id="early-text">ClientHello + 应用数据 </text><text class="taskTextOutsideRight taskTextOutside2  milestoneText width-67" y="303.5" x="90" font-size="11" id="app0-text">应用数据就绪           </text><text class="taskTextOutsideRight taskTextOutside2  milestoneText width-57" y="279.5" x="196" font-size="11" id="resp-text">服务器响应             </text><text class="taskTextOutsideRight taskTextOutside0  milestoneText width-91.06640625" y="87.5" x="301" font-size="11" id="sh12-text">ServerHello + Cert    </text><text class="taskTextOutsideRight taskTextOutside1  milestoneText width-109.875" y="207.5" x="301" font-size="11" id="fin13-text">ServerHello + Finished </text><text class="taskTextOutsideRight taskTextOutside1  milestoneText width-67" y="231.5" x="301" font-size="11" id="app13-text">应用数据就绪           </text><text class="taskTextOutsideRight taskTextOutside0  milestoneText width-92.921875" y="111.5" x="513" font-size="11" id="cke12-text">ClientKeyExchange     </text><text class="taskTextOutsideLeft taskTextOutside0  milestoneText" y="135.5" x="694" font-size="11" id="fin12-text">Finished              </text><text class="taskTextOutsideLeft taskTextOutside0  milestoneText" y="159.5" x="694" font-size="11" id="app12-text">应用数据就绪          </text></g><g><text class="sectionTitle sectionTitle0" font-size="11" y="110" x="10" dy="0em"><tspan x="10" alignment-baseline="central">TLS 1.2 (2-RTT)</tspan></text><text class="sectionTitle sectionTitle1" font-size="11" y="206" x="10" dy="0em"><tspan x="10" alignment-baseline="central">TLS 1.3 (1-RTT)  </tspan></text><text class="sectionTitle sectionTitle2" font-size="11" y="278" x="10" dy="0em"><tspan x="10" alignment-baseline="central">TLS 1.3 (0-RTT)</tspan></text></g><g class="today"><line class="today" y2="339" y1="25" x2="7392536684" x1="7392536684"/></g><text class="titleText" y="25" x="392">TLS握手性能对比 (时间轴)</text></svg>