# CS3611 邮件客户端项目依赖文件
# 包含运行完整邮件系统所需的所有依赖

# ============================================================================
# 核心依赖 (必需)
# ============================================================================
python-dotenv>=1.0.0  # 环境变量管理
cryptography>=41.0.0  # 加密功能和SSL/TLS支持
pyOpenSSL>=23.0.0     # SSL/TLS连接

# ============================================================================
# 邮件服务器依赖 (服务器端必需)
# ============================================================================
aiosmtpd>=1.4.4       # 异步SMTP服务器框架

# ============================================================================
# 数据库依赖 (可选，使用高级功能时需要)
# ============================================================================
SQLAlchemy>=2.0.0     # ORM框架
alembic>=1.12.0       # 数据库迁移工具

# ============================================================================
# 邮件处理依赖
# ============================================================================
dnspython>=2.4.0      # DNS查询功能

# ============================================================================
# Web框架和HTTP处理依赖
# ============================================================================
Flask>=2.3.0           # Web框架
Flask-CORS>=4.0.0      # 跨域资源共享
Flask-WTF>=1.2.0      # 表单处理
Flask-Login>=0.6.2    # 用户认证
Flask-Session>=0.5.0  # Session管理
Werkzeug>=2.3.0        # WSGI工具库，Flask的核心

# ============================================================================
# 测试框架依赖
# ============================================================================
pytest>=7.4.0         # 测试框架
pytest-cov>=4.1.0     # 测试覆盖率
pytest-asyncio>=0.21.0 # 异步测试支持

# ============================================================================
# PGP端到端加密依赖
# ============================================================================
python-gnupg>=0.5.2   # PGP加密和签名支持
pgpy>=0.6.0           # 纯Python PGP实现
scapy>=2.5.0           # 网络数据包处理与流量分析（演示用）
